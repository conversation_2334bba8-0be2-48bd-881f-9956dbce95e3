declare module 'react-native-ssh-sftp' {
  export interface SSHClient {
    connect(callback: (error: Error | null) => void): void;
    execute(command: string, callback: (error: Error | null, output: string) => void): void;
    disconnect(callback: () => void): void;
    shell(callback: (error: Error | null, shell: any) => void): void;
    uploadFile(localPath: string, remotePath: string, callback: (error: Error | null) => void): void;
    downloadFile(remotePath: string, localPath: string, callback: (error: Error | null) => void): void;
    cancel(): void;
  }

  export default class SSHClient {
    constructor(
      host: string,
      port: number,
      username: string,
      password?: string,
      privateKey?: string,
      passphrase?: string
    );
    connect(callback: (error: Error | null) => void): void;
    execute(command: string, callback: (error: Error | null, output: string) => void): void;
    disconnect(callback: () => void): void;
    shell(callback: (error: Error | null, shell: any) => void): void;
    uploadFile(localPath: string, remotePath: string, callback: (error: Error | null) => void): void;
    downloadFile(remotePath: string, localPath: string, callback: (error: Error | null) => void): void;
    cancel(): void;
  }
}