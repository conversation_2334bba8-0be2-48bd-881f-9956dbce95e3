import { SSHConfig, ConnectionStatus, FileSystemItem } from '../types';

// Check if native SSH module is available
let SSHClient: any = null;
try {
  SSHClient = require('react-native-ssh-sftp').default;
} catch (error) {
  console.log('SSH native module not available, using mock implementation');
}

export class SSHService {
  private connection: SSHClient | null = null;
  private status: ConnectionStatus = ConnectionStatus.DISCONNECTED;
  private statusCallbacks: ((status: ConnectionStatus) => void)[] = [];

  constructor() {
    // 初始化SSH服务
  }

  async connect(config: SSHConfig): Promise<void> {
    try {
      this.setStatus(ConnectionStatus.CONNECTING);
      
      if (SSHClient) {
        // 使用原生SSH实现
        const sshClient = new SSHClient(
          config.host,
          config.port,
          config.username,
          config.password,
          config.privateKey,
          config.passphrase
        );

        await new Promise<void>((resolve, reject) => {
          sshClient.connect((error: Error | null) => {
            if (error) {
              reject(error);
            } else {
              resolve();
            }
          });
        });

        this.connection = sshClient;
      } else {
        // 使用模拟实现（用于Expo Go开发）
        await new Promise(resolve => setTimeout(resolve, 2000));
        this.connection = {
          config,
          connected: true,
          execute: (command: string, callback: (error: Error | null, output: string) => void) => {
            const output = this.getMockResponse(command);
            callback(null, output);
          },
          disconnect: (callback: () => void) => {
            callback();
          }
        } as any;
      }

      this.setStatus(ConnectionStatus.CONNECTED);
    } catch (error) {
      this.setStatus(ConnectionStatus.ERROR);
      throw new Error(`SSH connection failed: ${error}`);
    }
  }

  async disconnect(): Promise<void> {
    if (this.connection) {
      if (SSHClient) {
        // 原生实现
        return new Promise<void>((resolve) => {
          this.connection?.disconnect(() => {
            this.connection = null;
            this.setStatus(ConnectionStatus.DISCONNECTED);
            resolve();
          });
        });
      } else {
        // 模拟实现
        this.connection = null;
      }
    }
    this.setStatus(ConnectionStatus.DISCONNECTED);
  }

  async executeCommand(command: string): Promise<string> {
    if (!this.isConnected()) {
      throw new Error('SSH connection not established');
    }

    try {
      console.log(`Executing command: ${command}`);
      
      if (SSHClient) {
        // 原生实现
        return new Promise<string>((resolve, reject) => {
          this.connection?.execute(command, (error: Error | null, output: string) => {
            if (error) {
              reject(new Error(`Command execution failed: ${error}`));
            } else {
              resolve(output);
            }
          });
        });
      } else {
        // 模拟实现
        return new Promise<string>((resolve) => {
          setTimeout(() => {
            resolve(this.getMockResponse(command));
          }, 500);
        });
      }
    } catch (error) {
      throw new Error(`Command execution failed: ${error}`);
    }
  }

  async listDirectory(path: string): Promise<FileSystemItem[]> {
    const command = `ls -la "${path}"`;
    const output = await this.executeCommand(command);
    return this.parseFileList(output, path);
  }

  async readFile(filePath: string): Promise<string> {
    const command = `cat "${filePath}"`;
    return await this.executeCommand(command);
  }

  async writeFile(filePath: string, content: string): Promise<void> {
    const command = `echo '${content.replace(/'/g, "'\\''")}' > "${filePath}"`;
    await this.executeCommand(command);
  }

  isConnected(): boolean {
    return this.status === ConnectionStatus.CONNECTED && this.connection !== null;
  }

  getStatus(): ConnectionStatus {
    return this.status;
  }

  onStatusChange(callback: (status: ConnectionStatus) => void): void {
    this.statusCallbacks.push(callback);
  }

  private setStatus(status: ConnectionStatus): void {
    this.status = status;
    this.statusCallbacks.forEach(callback => callback(status));
  }

  private getMockFileList(command: string): string {
    // 模拟ls命令输出
    return `total 24
drwxr-xr-x  5 <USER> <GROUP> 4096 Jan 15 10:30 .
drwxr-xr-x  3 <USER> <GROUP> 4096 Jan 15 10:00 ..
-rw-r--r--  1 <USER> <GROUP>  156 Jan 15 10:30 README.md
drwxr-xr-x  2 <USER> <GROUP> 4096 Jan 15 10:25 src
-rw-r--r--  1 <USER> <GROUP>  523 Jan 15 10:20 package.json
-rw-r--r--  1 <USER> <GROUP> 1024 Jan 15 10:15 main.py`;
  }

  private getMockFileContent(command: string): string {
    const fileName = command.split(' ').pop();
    if (fileName?.includes('README.md')) {
      return '# Remote Coding Project\n\nThis is a sample project for remote coding.';
    } else if (fileName?.includes('main.py')) {
      return `#!/usr/bin/env python3
def main():
    print("Hello, World!")
    
if __name__ == "__main__":
    main()`;
    } else if (fileName?.includes('package.json')) {
      return `{
  "name": "remote-project",
  "version": "1.0.0",
  "main": "index.js"
}`;
    }
    return 'File content not found';
  }

  private getMockResponse(command: string): string {
    if (command.startsWith('ls')) {
      return `total 24
  drwxr-xr-x  5 <USER> <GROUP> 4096 Jan 15 10:30 .
  drwxr-xr-x  3 <USER> <GROUP> 4096 Jan 15 10:00 ..
  -rw-r--r--  1 <USER> <GROUP>  156 Jan 15 10:30 README.md
  drwxr-xr-x  2 <USER> <GROUP> 4096 Jan 15 10:25 src
  -rw-r--r--  1 <USER> <GROUP>  523 Jan 15 10:20 package.json
  -rw-r--r--  1 <USER> <GROUP> 1024 Jan 15 10:15 main.py`;
    } else if (command.startsWith('cat')) {
      const fileName = command.split(' ').pop();
      if (fileName?.includes('README.md')) {
        return '# Remote Coding Project\n\nThis is a sample project for remote coding.';
      } else if (fileName?.includes('main.py')) {
        return `#!/usr/bin/env python3
  def main():
      print("Hello, World!")
      
  if __name__ == "__main__":
      main()`;
      } else if (fileName?.includes('package.json')) {
        return `{
    "name": "remote-project",
    "version": "1.0.0",
    "main": "index.js"
  }`;
      }
    } else if (command.includes('claude-code')) {
      return 'Claude Code started successfully';
    }
    return `Command executed: ${command}`;
  }

  private parseFileList(output: string, basePath: string): FileSystemItem[] {
    const lines = output.split('\n').filter(line => line.trim() && !line.startsWith('total'));
    const items: FileSystemItem[] = [];

    for (const line of lines) {
      const parts = line.trim().split(/\s+/);
      if (parts.length < 9) continue;

      const permissions = parts[0];
      const size = parseInt(parts[4]) || 0;
      const name = parts.slice(8).join(' ');
      
      if (name === '.' || name === '..') continue;

      const type = permissions.startsWith('d') ? 'directory' : 'file';
      const path = `${basePath}/${name}`.replace(/\/+/g, '/');

      items.push({
        name,
        path,
        type,
        size,
        permissions,
        modifiedTime: new Date()
      });
    }

    return items;
  }
}
