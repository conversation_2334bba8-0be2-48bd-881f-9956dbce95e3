/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./App.{js,ts,tsx}', './src/**/*.{js,ts,tsx}'],

  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      colors: {
        dark: {
          bg: '#1a1a1a',        // 主背景色
          surface: '#2a2a2a',   // 卡片/表面背景
          border: '#404040',    // 边框颜色
          text: '#e0e0e0',      // 主文本颜色
          'text-secondary': '#a0a0a0', // 次要文本颜色
          'text-muted': '#707070',      // 静音文本颜色
          accent: '#4ade80',    // 强调色（绿色）
          'accent-hover': '#22c55e',
          button: '#374151',    // 按钮背景
          'button-hover': '#4b5563',
          input: '#374151',     // 输入框背景
          'input-border': '#6b7280',
        }
      },
    },
  },
  plugins: [],
};
